// Chinook Database Schema Definition (DBML v2.6+)
// Enterprise-grade music store database with hybrid hierarchical categories
// Created: 2025-01-06
// Version: 2.0.0
// Architecture: Laravel 12 + Hybrid Hierarchical Data Management

Project chinook_enterprise {
  database_type: 'MySQL'
  Note: '''
    # Chinook Enterprise Database Schema

    Modern Laravel 12 implementation of the Chinook music store database with:
    - Hybrid hierarchical category management (closure table + adjacency list)
    - Role-based access control (RBAC) with Spatie <PERSON> Permission
    - Polymorphic categorization system replacing traditional genres
    - Modern Laravel features: timestamps, soft deletes, user stamps, secondary keys, slugs
    - Enterprise-grade performance optimization and indexing

    ## CategoryType Enum Values:
    - GENRE: Music genres (Rock, Jazz, Classical, etc.)
    - MOOD: Emotional categories (Energetic, Relaxing, Melancholic, etc.)
    - THEME: Thematic categories (Workout, Study, Party, etc.)
    - ERA: Time periods (1960s, 1970s, 1980s, etc.)
    - INSTRUMENT: Instrument focus (Piano, Guitar, Orchestral, etc.)
    - LANGUAGE: Language categories (English, Spanish, Instrumental, etc.)
    - OCCASION: Event categories (Wedding, Birthday, Holiday, etc.)
  '''
}

// Core Music Tables
Table artists {
  id bigint [pk, increment, note: 'Primary key']
  public_id varchar(26) [unique, not null, note: 'ULID - Public-facing identifier for APIs']
  slug varchar(255) [unique, not null, note: 'URL-friendly identifier generated from public_id']
  name varchar(255) [not null, note: 'Artist or band name']
  biography text [note: 'Artist biography and background information']
  website varchar(500) [note: 'Official website URL']
  social_links json [note: 'Social media links and profiles']
  country varchar(100) [note: 'Country of origin']
  formed_year int [note: 'Year the artist/band was formed']
  is_active boolean [default: true, not null, note: 'Whether the artist is currently active']

  // Laravel 12 Modern Features
  created_by bigint [ref: > users.id, note: 'User who created this record']
  updated_by bigint [ref: > users.id, note: 'User who last updated this record']
  created_at timestamp [default: `CURRENT_TIMESTAMP`, note: 'Record creation timestamp']
  updated_at timestamp [default: `CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`, note: 'Last update timestamp']
  deleted_at timestamp [null, note: 'Soft delete timestamp']

  indexes {
    (public_id) [unique, name: 'artists_public_id_unique']
    (slug) [unique, name: 'artists_slug_unique']
    (name) [name: 'artists_name_index']
    (country) [name: 'artists_country_index']
    (is_active) [name: 'artists_is_active_index']
    (created_by) [name: 'artists_created_by_index']
    (updated_by) [name: 'artists_updated_by_index']
    (deleted_at) [name: 'artists_deleted_at_index']
  }

  Note: 'Music artists and bands with polymorphic category relationships and modern Laravel features'
}

Table albums {
  id bigint [pk, increment, note: 'Primary key']
  public_id varchar(26) [unique, not null, note: 'ULID - Public-facing identifier for APIs']
  slug varchar(255) [unique, not null, note: 'URL-friendly identifier generated from public_id']
  artist_id bigint [ref: > artists.id, not null, note: 'Reference to the artist who created this album']
  title varchar(255) [not null, note: 'Album title']
  release_date date [note: 'Album release date']
  label varchar(255) [note: 'Record label']
  catalog_number varchar(100) [note: 'Catalog number from the record label']
  description text [note: 'Album description and notes']
  cover_image_url varchar(500) [note: 'URL to album cover art']
  total_tracks int [default: 0, note: 'Total number of tracks on the album']
  total_duration_ms bigint [default: 0, note: 'Total album duration in milliseconds']
  is_compilation boolean [default: false, note: 'Whether this is a compilation album']
  is_explicit boolean [default: false, note: 'Whether the album contains explicit content']
  is_active boolean [default: true, not null, note: 'Whether the album is currently active/available']

  // Laravel 12 Modern Features
  created_by bigint [ref: > users.id, note: 'User who created this record']
  updated_by bigint [ref: > users.id, note: 'User who last updated this record']
  created_at timestamp [default: `CURRENT_TIMESTAMP`, note: 'Record creation timestamp']
  updated_at timestamp [default: `CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`, note: 'Last update timestamp']
  deleted_at timestamp [null, note: 'Soft delete timestamp']

  indexes {
    (public_id) [unique, name: 'albums_public_id_unique']
    (slug) [unique, name: 'albums_slug_unique']
    (artist_id) [name: 'albums_artist_id_index']
    (title) [name: 'albums_title_index']
    (release_date) [name: 'albums_release_date_index']
    (is_active) [name: 'albums_is_active_index']
    (created_by) [name: 'albums_created_by_index']
    (updated_by) [name: 'albums_updated_by_index']
    (deleted_at) [name: 'albums_deleted_at_index']
  }

  Note: 'Albums belonging to artists with enhanced metadata and polymorphic categories'
}

Table tracks {
  id bigint [pk, increment, note: 'Primary key']
  public_id varchar(19) [unique, not null, note: 'Snowflake ID - High-performance identifier for large datasets']
  slug varchar(255) [unique, not null, note: 'URL-friendly identifier generated from public_id']
  album_id bigint [ref: > albums.id, not null, note: 'Reference to the album containing this track']
  media_type_id bigint [ref: > media_types.id, not null, note: 'Reference to the media type/format']
  name varchar(255) [not null, note: 'Track name/title']
  composer varchar(255) [note: 'Composer of the track']
  milliseconds bigint [not null, note: 'Track duration in milliseconds']
  bytes bigint [note: 'File size in bytes']
  unit_price decimal(10,2) [not null, note: 'Price per track for purchase']
  track_number int [note: 'Track number on the album']
  disc_number int [default: 1, note: 'Disc number for multi-disc albums']
  is_explicit boolean [default: false, note: 'Whether the track contains explicit content']
  is_active boolean [default: true, not null, note: 'Whether the track is currently active/available']
  preview_url varchar(500) [note: 'URL to track preview/sample']
  lyrics text [note: 'Track lyrics']

  // Laravel 12 Modern Features
  created_by bigint [ref: > users.id, note: 'User who created this record']
  updated_by bigint [ref: > users.id, note: 'User who last updated this record']
  created_at timestamp [default: `CURRENT_TIMESTAMP`, note: 'Record creation timestamp']
  updated_at timestamp [default: `CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`, note: 'Last update timestamp']
  deleted_at timestamp [null, note: 'Soft delete timestamp']

  indexes {
    (public_id) [unique, name: 'tracks_public_id_unique']
    (slug) [unique, name: 'tracks_slug_unique']
    (album_id) [name: 'tracks_album_id_index']
    (media_type_id) [name: 'tracks_media_type_id_index']
    (name) [name: 'tracks_name_index']
    (unit_price) [name: 'tracks_unit_price_index']
    (is_active) [name: 'tracks_is_active_index']
    (created_by) [name: 'tracks_created_by_index']
    (updated_by) [name: 'tracks_updated_by_index']
    (deleted_at) [name: 'tracks_deleted_at_index']
    (album_id, track_number) [name: 'tracks_album_track_number_index']
  }

  Note: 'Individual songs with polymorphic categories (replaces traditional genre_id foreign key)'
}

Table media_types {
  id bigint [pk, increment, note: 'Primary key']
  public_id varchar(36) [unique, not null, note: 'UUID - Standards-compliant identifier for reference data']
  slug varchar(255) [unique, not null, note: 'URL-friendly identifier generated from public_id']
  name varchar(255) [not null, note: 'Media type name (MP3, AAC, FLAC, etc.)']
  mime_type varchar(100) [note: 'MIME type for the media format']
  file_extension varchar(10) [note: 'File extension (mp3, aac, flac, etc.)']
  description text [note: 'Description of the media type and its characteristics']
  is_active boolean [default: true, not null, note: 'Whether this media type is currently supported']

  // Laravel 12 Modern Features
  created_by bigint [ref: > users.id, note: 'User who created this record']
  updated_by bigint [ref: > users.id, note: 'User who last updated this record']
  created_at timestamp [default: `CURRENT_TIMESTAMP`, note: 'Record creation timestamp']
  updated_at timestamp [default: `CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`, note: 'Last update timestamp']
  deleted_at timestamp [null, note: 'Soft delete timestamp']

  indexes {
    (public_id) [unique, name: 'media_types_public_id_unique']
    (slug) [unique, name: 'media_types_slug_unique']
    (name) [unique, name: 'media_types_name_unique']
    (mime_type) [name: 'media_types_mime_type_index']
    (file_extension) [name: 'media_types_file_extension_index']
    (is_active) [name: 'media_types_is_active_index']
    (created_by) [name: 'media_types_created_by_index']
    (updated_by) [name: 'media_types_updated_by_index']
    (deleted_at) [name: 'media_types_deleted_at_index']
  }

  Note: 'File formats (MP3, AAC, FLAC, etc.) with enhanced metadata and technical specifications'
}

// Hybrid Hierarchical Category System
// Combines closure table and adjacency list patterns for optimal performance
Table categories {
  id bigint [pk, increment, note: 'Primary key']
  public_id varchar(36) [unique, not null, note: 'UUID - Standards-compliant identifier for reference data']
  slug varchar(255) [unique, not null, note: 'URL-friendly identifier generated from public_id']

  // Adjacency List Pattern (for fast writes and simple queries)
  parent_id bigint [ref: > categories.id, null, note: 'Direct parent category (adjacency list pattern)']
  depth int [default: 0, not null, note: 'Hierarchy depth level (0 = root)']
  path varchar(500) [note: 'Materialized path for quick ancestor queries']

  // Category Data
  name varchar(255) [not null, note: 'Category name']
  description text [note: 'Category description and usage notes']
  type enum('genre', 'mood', 'theme', 'era', 'instrument', 'language', 'occasion') [not null, note: 'CategoryType enum value']
  sort_order int [default: 0, note: 'Display sort order within parent']
  is_active boolean [default: true, not null, note: 'Whether the category is currently active']
  color varchar(7) [note: 'Hex color code for UI representation']
  icon varchar(100) [note: 'Font Awesome icon class for visual identification']
  metadata json [note: 'Additional category metadata and configuration']

  // Laravel 12 Modern Features
  created_by bigint [ref: > users.id, note: 'User who created this record']
  updated_by bigint [ref: > users.id, note: 'User who last updated this record']
  created_at timestamp [default: `CURRENT_TIMESTAMP`, note: 'Record creation timestamp']
  updated_at timestamp [default: `CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`, note: 'Last update timestamp']
  deleted_at timestamp [null, note: 'Soft delete timestamp']

  indexes {
    (public_id) [unique, name: 'categories_public_id_unique']
    (slug) [unique, name: 'categories_slug_unique']
    (parent_id) [name: 'categories_parent_id_index']
    (type) [name: 'categories_type_index']
    (name) [name: 'categories_name_index']
    (depth) [name: 'categories_depth_index']
    (sort_order) [name: 'categories_sort_order_index']
    (is_active) [name: 'categories_is_active_index']
    (created_by) [name: 'categories_created_by_index']
    (updated_by) [name: 'categories_updated_by_index']
    (deleted_at) [name: 'categories_deleted_at_index']
    (type, parent_id) [name: 'categories_type_parent_index']
    (parent_id, sort_order) [name: 'categories_parent_sort_index']
  }

  Note: '''
    Hybrid hierarchical polymorphic categorization system replacing traditional genres.
    Uses both adjacency list (parent_id, depth, path) and closure table patterns.
    Supports 7 category types: GENRE, MOOD, THEME, ERA, INSTRUMENT, LANGUAGE, OCCASION.
  '''
}

// Closure Table Pattern (for efficient hierarchical queries and analytics)
Table category_closure {
  ancestor_id bigint [ref: > categories.id, not null, note: 'Ancestor category in the hierarchy']
  descendant_id bigint [ref: > categories.id, not null, note: 'Descendant category in the hierarchy']
  depth int [not null, note: 'Number of levels between ancestor and descendant (0 = self-reference)']

  created_at timestamp [default: `CURRENT_TIMESTAMP`, note: 'Record creation timestamp']
  updated_at timestamp [default: `CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`, note: 'Last update timestamp']

  indexes {
    (ancestor_id, descendant_id) [pk, name: 'category_closure_primary']
    (descendant_id) [name: 'category_closure_descendant_index']
    (depth) [name: 'category_closure_depth_index']
    (ancestor_id, depth) [name: 'category_closure_ancestor_depth_index']
    (descendant_id, depth) [name: 'category_closure_descendant_depth_index']
  }

  Note: '''
    Closure table for efficient hierarchical category queries.
    Stores all ancestor-descendant relationships with depth information.
    Enables fast queries for subtrees, ancestors, descendants, and hierarchy analytics.
  '''
}

// Polymorphic Pivot Table for Category Assignments
Table categorizables {
  category_id bigint [ref: > categories.id, not null, note: 'Reference to the category']
  categorizable_id bigint [not null, note: 'ID of the categorized model (polymorphic)']
  categorizable_type varchar(255) [not null, note: 'Class name of the categorized model (polymorphic)']
  metadata json [note: 'Additional relationship metadata (confidence scores, user ratings, etc.)']
  sort_order int [default: 0, note: 'Display sort order for this category assignment']
  is_primary boolean [default: false, note: 'Whether this is the primary category for this type']

  // Laravel 12 Modern Features
  created_by bigint [ref: > users.id, note: 'User who created this relationship']
  updated_by bigint [ref: > users.id, note: 'User who last updated this relationship']
  created_at timestamp [default: `CURRENT_TIMESTAMP`, note: 'Record creation timestamp']
  updated_at timestamp [default: `CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`, note: 'Last update timestamp']

  indexes {
    (category_id, categorizable_id, categorizable_type) [pk, name: 'categorizables_primary']
    (categorizable_id, categorizable_type) [name: 'categorizables_categorizable_index']
    (category_id) [name: 'categorizables_category_id_index']
    (is_primary) [name: 'categorizables_is_primary_index']
    (created_by) [name: 'categorizables_created_by_index']
    (updated_by) [name: 'categorizables_updated_by_index']
  }

  Note: '''
    Polymorphic many-to-many relationship table for category assignments.
    Allows any model (artists, albums, tracks, playlists, customers) to be categorized.
    Supports multiple categories per model and primary category designation.
  '''
}

// Customer Management Tables
Table customers {
  id bigint [pk, increment, note: 'Primary key']
  public_id varchar(26) [unique, not null, note: 'ULID - Public-facing identifier for APIs']
  slug varchar(255) [unique, not null, note: 'URL-friendly identifier generated from public_id']
  support_rep_id bigint [ref: > employees.id, note: 'Assigned customer support representative']
  first_name varchar(100) [not null, note: 'Customer first name']
  last_name varchar(100) [not null, note: 'Customer last name']
  company varchar(255) [note: 'Company name (for business customers)']
  address varchar(255) [note: 'Street address']
  city varchar(100) [note: 'City']
  state varchar(100) [note: 'State or province']
  country varchar(100) [note: 'Country']
  postal_code varchar(20) [note: 'Postal or ZIP code']
  phone varchar(50) [note: 'Phone number']
  fax varchar(50) [note: 'Fax number']
  email varchar(255) [unique, not null, note: 'Email address (unique)']
  date_of_birth date [note: 'Date of birth for age verification and marketing']
  preferences json [note: 'Customer preferences and settings']
  is_active boolean [default: true, not null, note: 'Whether the customer account is active']

  // Laravel 12 Modern Features
  created_by bigint [ref: > users.id, note: 'User who created this record']
  updated_by bigint [ref: > users.id, note: 'User who last updated this record']
  created_at timestamp [default: `CURRENT_TIMESTAMP`, note: 'Record creation timestamp']
  updated_at timestamp [default: `CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`, note: 'Last update timestamp']
  deleted_at timestamp [null, note: 'Soft delete timestamp']

  indexes {
    (public_id) [unique, name: 'customers_public_id_unique']
    (slug) [unique, name: 'customers_slug_unique']
    (email) [unique, name: 'customers_email_unique']
    (support_rep_id) [name: 'customers_support_rep_id_index']
    (last_name, first_name) [name: 'customers_name_index']
    (city) [name: 'customers_city_index']
    (country) [name: 'customers_country_index']
    (is_active) [name: 'customers_is_active_index']
    (created_by) [name: 'customers_created_by_index']
    (updated_by) [name: 'customers_updated_by_index']
    (deleted_at) [name: 'customers_deleted_at_index']
  }

  Note: 'Customer information with support representatives, preferences, and polymorphic category relationships'
}

Table employees {
  id bigint [pk, increment, note: 'Primary key']
  public_id varchar(26) [unique, not null, note: 'ULID - Public-facing identifier for APIs']
  slug varchar(255) [unique, not null, note: 'URL-friendly identifier generated from public_id']
  reports_to bigint [ref: > employees.id, note: 'Manager/supervisor reference (self-referencing)']
  last_name varchar(100) [not null, note: 'Employee last name']
  first_name varchar(100) [not null, note: 'Employee first name']
  title varchar(255) [note: 'Job title']
  email varchar(255) [unique, not null, note: 'Employee email address (unique)']
  phone varchar(50) [note: 'Phone number']
  fax varchar(50) [note: 'Fax number']
  birth_date date [note: 'Date of birth']
  hire_date date [note: 'Date of hire']
  address varchar(255) [note: 'Street address']
  city varchar(100) [note: 'City']
  state varchar(100) [note: 'State or province']
  country varchar(100) [note: 'Country']
  postal_code varchar(20) [note: 'Postal or ZIP code']
  salary decimal(10,2) [note: 'Employee salary']
  is_active boolean [default: true, not null, note: 'Whether the employee is currently active']

  // Laravel 12 Modern Features
  created_by bigint [ref: > users.id, note: 'User who created this record']
  updated_by bigint [ref: > users.id, note: 'User who last updated this record']
  created_at timestamp [default: `CURRENT_TIMESTAMP`, note: 'Record creation timestamp']
  updated_at timestamp [default: `CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`, note: 'Last update timestamp']
  deleted_at timestamp [null, note: 'Soft delete timestamp']

  indexes {
    (public_id) [unique, name: 'employees_public_id_unique']
    (slug) [unique, name: 'employees_slug_unique']
    (email) [unique, name: 'employees_email_unique']
    (reports_to) [name: 'employees_reports_to_index']
    (last_name, first_name) [name: 'employees_name_index']
    (title) [name: 'employees_title_index']
    (hire_date) [name: 'employees_hire_date_index']
    (is_active) [name: 'employees_is_active_index']
    (created_by) [name: 'employees_created_by_index']
    (updated_by) [name: 'employees_updated_by_index']
    (deleted_at) [name: 'employees_deleted_at_index']
  }

  Note: 'Company employees with hierarchical relationships, RBAC integration, and customer support assignments'
}

// Sales System Tables
Table invoices {
  id bigint [pk, increment, note: 'Primary key']
  public_id varchar(26) [unique, not null, note: 'ULID - Public-facing identifier for APIs']
  slug varchar(255) [unique, not null, note: 'URL-friendly identifier generated from public_id']
  customer_id bigint [ref: > customers.id, not null, note: 'Reference to the purchasing customer']
  invoice_date date [not null, note: 'Date the invoice was created']
  billing_address varchar(255) [note: 'Billing street address']
  billing_city varchar(100) [note: 'Billing city']
  billing_state varchar(100) [note: 'Billing state or province']
  billing_country varchar(100) [note: 'Billing country']
  billing_postal_code varchar(20) [note: 'Billing postal or ZIP code']
  total decimal(10,2) [not null, note: 'Total invoice amount']
  status varchar(50) [default: 'pending', note: 'Invoice status (pending, paid, cancelled, refunded)']
  payment_details json [note: 'Payment method and transaction information']

  // Laravel 12 Modern Features
  created_by bigint [ref: > users.id, note: 'User who created this record']
  updated_by bigint [ref: > users.id, note: 'User who last updated this record']
  created_at timestamp [default: `CURRENT_TIMESTAMP`, note: 'Record creation timestamp']
  updated_at timestamp [default: `CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`, note: 'Last update timestamp']
  deleted_at timestamp [null, note: 'Soft delete timestamp']

  indexes {
    (public_id) [unique, name: 'invoices_public_id_unique']
    (slug) [unique, name: 'invoices_slug_unique']
    (customer_id) [name: 'invoices_customer_id_index']
    (invoice_date) [name: 'invoices_invoice_date_index']
    (status) [name: 'invoices_status_index']
    (total) [name: 'invoices_total_index']
    (created_by) [name: 'invoices_created_by_index']
    (updated_by) [name: 'invoices_updated_by_index']
    (deleted_at) [name: 'invoices_deleted_at_index']
  }

  Note: 'Customer purchase records with enhanced tracking, payment details, and status management'
}

Table invoice_lines {
  id bigint [pk, increment, note: 'Primary key']
  invoice_id bigint [ref: > invoices.id, not null, note: 'Reference to the parent invoice']
  track_id bigint [ref: > tracks.id, not null, note: 'Reference to the purchased track']
  unit_price decimal(10,2) [not null, note: 'Price per unit at time of purchase']
  quantity int [default: 1, not null, note: 'Quantity purchased']
  line_total decimal(10,2) [not null, note: 'Total amount for this line item']

  created_at timestamp [default: `CURRENT_TIMESTAMP`, note: 'Record creation timestamp']
  updated_at timestamp [default: `CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`, note: 'Last update timestamp']

  indexes {
    (invoice_id) [name: 'invoice_lines_invoice_id_index']
    (track_id) [name: 'invoice_lines_track_id_index']
    (invoice_id, track_id) [unique, name: 'invoice_lines_invoice_track_unique']
  }

  Note: 'Individual items purchased on each invoice with pricing and quantity information'
}

// Playlist System Tables
Table playlists {
  id bigint [pk, increment, note: 'Primary key']
  public_id varchar(26) [unique, not null, note: 'ULID - Public-facing identifier for APIs']
  slug varchar(255) [unique, not null, note: 'URL-friendly identifier generated from public_id']
  user_id bigint [ref: > users.id, not null, note: 'Reference to the playlist owner']
  name varchar(255) [not null, note: 'Playlist name']
  description text [note: 'Playlist description and notes']
  is_public boolean [default: false, note: 'Whether the playlist is publicly visible']
  is_collaborative boolean [default: false, note: 'Whether others can edit this playlist']
  total_tracks int [default: 0, note: 'Total number of tracks in the playlist']
  total_duration_ms bigint [default: 0, note: 'Total playlist duration in milliseconds']

  // Laravel 12 Modern Features
  created_by bigint [ref: > users.id, note: 'User who created this record']
  updated_by bigint [ref: > users.id, note: 'User who last updated this record']
  created_at timestamp [default: `CURRENT_TIMESTAMP`, note: 'Record creation timestamp']
  updated_at timestamp [default: `CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`, note: 'Last update timestamp']
  deleted_at timestamp [null, note: 'Soft delete timestamp']

  indexes {
    (public_id) [unique, name: 'playlists_public_id_unique']
    (slug) [unique, name: 'playlists_slug_unique']
    (user_id) [name: 'playlists_user_id_index']
    (name) [name: 'playlists_name_index']
    (is_public) [name: 'playlists_is_public_index']
    (is_collaborative) [name: 'playlists_is_collaborative_index']
    (created_by) [name: 'playlists_created_by_index']
    (updated_by) [name: 'playlists_updated_by_index']
    (deleted_at) [name: 'playlists_deleted_at_index']
  }

  Note: 'User-created music playlists with polymorphic categories, collaboration features, and privacy controls'
}

Table playlist_track {
  playlist_id bigint [ref: > playlists.id, not null, note: 'Reference to the playlist']
  track_id bigint [ref: > tracks.id, not null, note: 'Reference to the track']
  sort_order int [not null, note: 'Order of the track within the playlist']
  added_at timestamp [default: `CURRENT_TIMESTAMP`, note: 'When the track was added to the playlist']
  added_by bigint [ref: > users.id, note: 'User who added the track to the playlist']

  created_at timestamp [default: `CURRENT_TIMESTAMP`, note: 'Record creation timestamp']
  updated_at timestamp [default: `CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`, note: 'Last update timestamp']

  indexes {
    (playlist_id, track_id) [pk, name: 'playlist_track_primary']
    (track_id) [name: 'playlist_track_track_id_index']
    (playlist_id, sort_order) [name: 'playlist_track_playlist_sort_index']
    (added_by) [name: 'playlist_track_added_by_index']
  }

  Note: 'Many-to-many relationship between playlists and tracks with ordering and audit information'
}

// Reference to Users table (assumed to exist from Laravel authentication)
Table users {
  id bigint [pk, increment, note: 'Primary key']
  name varchar(255) [not null, note: 'User full name']
  email varchar(255) [unique, not null, note: 'User email address']
  email_verified_at timestamp [null, note: 'Email verification timestamp']
  password varchar(255) [not null, note: 'Hashed password']
  remember_token varchar(100) [null, note: 'Remember me token']
  created_at timestamp [default: `CURRENT_TIMESTAMP`, note: 'Record creation timestamp']
  updated_at timestamp [default: `CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`, note: 'Last update timestamp']

  indexes {
    (email) [unique, name: 'users_email_unique']
  }

  Note: 'Laravel authentication users table for user stamps and RBAC integration'
}
